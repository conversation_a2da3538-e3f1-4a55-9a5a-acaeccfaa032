<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.customer.mapper.ValueAddedStockMapper">
    
    <resultMap type="com.bxm.customer.domain.ValueAddedStock" id="ValueAddedStockResult">
        <result property="id"                column="id"                />
        <result property="deliveryOrderNo"  column="delivery_order_no" />
        <result property="period"           column="period"            />
        <result property="analysisResult"   column="analysis_result"   />
        <result property="stockCount"       column="stock_count"       />
        <result property="remark"           column="remark"            />
        <result property="createBy"         column="create_by"         />
        <result property="createTime"       column="create_time"       />
        <result property="updateBy"         column="update_by"         />
        <result property="updateTime"       column="update_time"       />
    </resultMap>

    <sql id="selectValueAddedStockVo">
        select id, delivery_order_no, period, analysis_result, stock_count, remark, create_by, create_time, update_by, update_time
        from c_value_added_stock
    </sql>

    <select id="selectByDeliveryOrderNo" parameterType="String" resultMap="ValueAddedStockResult">
        <include refid="selectValueAddedStockVo"/>
        where delivery_order_no = #{deliveryOrderNo}
        order by period
    </select>

    <select id="selectByDeliveryOrderNoAndPeriod" resultMap="ValueAddedStockResult">
        <include refid="selectValueAddedStockVo"/>
        where delivery_order_no = #{deliveryOrderNo} and period = #{period}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into c_value_added_stock(delivery_order_no, period, analysis_result, stock_count, remark, create_by, create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.deliveryOrderNo}, #{item.period}, #{item.analysisResult}, #{item.stockCount}, #{item.remark}, 
             #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <delete id="deleteByDeliveryOrderNo" parameterType="String">
        delete from c_value_added_stock where delivery_order_no = #{deliveryOrderNo}
    </delete>

</mapper>
