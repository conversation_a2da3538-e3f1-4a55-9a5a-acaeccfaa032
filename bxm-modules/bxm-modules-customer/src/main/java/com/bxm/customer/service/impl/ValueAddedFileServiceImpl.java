package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.dto.FileStatusInfo;
import com.bxm.customer.domain.enums.ValueAddedFileType;
import com.bxm.customer.domain.enums.ValueAddedProcessStatus;
import com.bxm.customer.mapper.ValueAddedFileMapper;
import com.bxm.customer.service.IValueAddedFileService;
import com.bxm.customer.service.IValueAddedStockService;
import com.bxm.file.api.RemoteFileService;
import com.bxm.file.api.domain.RemoteAliFileDTO;
import com.bxm.common.core.utils.spring.SpringUtils;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;

/**
 * 增值交付单文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Slf4j
@Service
public class ValueAddedFileServiceImpl extends ServiceImpl<ValueAddedFileMapper, ValueAddedFile>
        implements IValueAddedFileService {

    @Autowired
    private RemoteFileService remoteFileService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long savePersonnelExcelFile(MultipartFile excelFile, String deliveryOrderNo) throws Exception {
        log.info("Saving personnel excel file, fileName: {}, size: {}", excelFile.getOriginalFilename(), excelFile.getSize());

        // 1. 文件大小验证
        if (excelFile.getSize() > 2 * 1024 * 1024) { // 2MB限制
            throw new IllegalArgumentException("Excel文件大小不能超过2MB");
        }

        // 2. 上传文件到文件服务
        RemoteAliFileDTO uploadResult = remoteFileService.uploadFile(excelFile).getData();
        if (uploadResult == null) {
            throw new RuntimeException("文件上传失败");
        }

        // 3. 保存文件记录到数据库
        ValueAddedFile fileRecord = new ValueAddedFile();
        fileRecord.setDeliveryOrderNo(deliveryOrderNo);
        fileRecord.setFileName(excelFile.getOriginalFilename());
        fileRecord.setFileUrl(uploadResult.getUrl());
        fileRecord.setFileSize(excelFile.getSize());
        fileRecord.setFileType(ValueAddedFileType.PERSONNEL_EXCEL.getCode()); // 人员明细excel
        fileRecord.setIsDel(false);
        fileRecord.setCreateTime(LocalDateTime.now());
        fileRecord.setUpdateTime(LocalDateTime.now());

        // 初始化处理状态 - 使用统一的工具方法构建JSON
        String initialStatus = buildInitialStatusJson();
        fileRecord.setStatus(ValueAddedProcessStatus.PROCESSING.getCode());
        fileRecord.setRemark(initialStatus);

        if (!save(fileRecord)) {
            throw new RuntimeException("保存文件记录失败");
        }

        log.info("File record saved successfully, fileId: {}, fileUrl: {}", fileRecord.getId(), uploadResult.getUrl());

        return fileRecord.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveDeliveryOrderFile(MultipartFile file, String deliveryOrderNo) throws Exception {
        log.info("Saving delivery order file, fileName: {}, size: {}, deliveryOrderNo: {}",
                file.getOriginalFilename(), file.getSize(), deliveryOrderNo);

        // 1. 参数验证
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        if (deliveryOrderNo == null || deliveryOrderNo.trim().isEmpty()) {
            throw new IllegalArgumentException("交付单编号不能为空");
        }

        // 2. 文件大小验证
        if (file.getSize() > 2 * 1024 * 1024) { // 2MB限制
            throw new IllegalArgumentException("文件大小不能超过2MB");
        }

        // 3. 上传文件到文件服务
        RemoteAliFileDTO uploadResult = remoteFileService.uploadFile(file).getData();
        if (uploadResult == null) {
            throw new RuntimeException("文件上传失败");
        }

        // 4. 保存文件记录到数据库
        ValueAddedFile fileRecord = new ValueAddedFile();
        fileRecord.setDeliveryOrderNo(deliveryOrderNo.trim());
        fileRecord.setFileName(file.getOriginalFilename());
        fileRecord.setFileUrl(uploadResult.getUrl());
        fileRecord.setFileSize(file.getSize());
        fileRecord.setFileType(ValueAddedFileType.DELIVERY_ATTACHMENT.getCode()); // 交付单文件
        fileRecord.setIsDel(false);
        fileRecord.setCreateTime(LocalDateTime.now());
        fileRecord.setUpdateTime(LocalDateTime.now());

        // 5. 设置初始状态为处理完成（交付单文件无需异步处理）
        fileRecord.setStatus(ValueAddedProcessStatus.COMPLETED.getCode());
        fileRecord.setRemark("交付单文件上传成功");

        if (!save(fileRecord)) {
            throw new RuntimeException("保存文件记录失败");
        }

        log.info("Delivery order file saved successfully, fileId: {}, deliveryOrderNo: {}",
                fileRecord.getId(), deliveryOrderNo);
        return fileRecord.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveStockExcelFile(MultipartFile excelFile, String deliveryOrderNo) throws Exception {
        log.info("Saving stock excel file, fileName: {}, size: {}, deliveryOrderNo: {}",
                excelFile.getOriginalFilename(), excelFile.getSize(), deliveryOrderNo);

        // 1. 参数验证
        if (excelFile == null || excelFile.isEmpty()) {
            throw new IllegalArgumentException("Excel文件不能为空");
        }
        if (deliveryOrderNo == null || deliveryOrderNo.trim().isEmpty()) {
            throw new IllegalArgumentException("交付单编号不能为空");
        }

        // 2. 文件大小验证
        if (excelFile.getSize() > 5 * 1024 * 1024) { // 5MB限制
            throw new IllegalArgumentException("Excel文件大小不能超过5MB");
        }

        // 3. 上传文件到文件服务
        RemoteAliFileDTO uploadResult = remoteFileService.uploadFile(excelFile).getData();
        if (uploadResult == null) {
            throw new RuntimeException("文件上传失败");
        }

        // 4. 保存文件记录到数据库
        ValueAddedFile fileRecord = new ValueAddedFile();
        fileRecord.setDeliveryOrderNo(deliveryOrderNo.trim());
        fileRecord.setFileName(excelFile.getOriginalFilename());
        fileRecord.setFileUrl(uploadResult.getUrl());
        fileRecord.setFileSize(excelFile.getSize());
        fileRecord.setFileType(ValueAddedFileType.STOCK.getCode()); // 库存Excel文件
        fileRecord.setIsDel(false);
        fileRecord.setCreateTime(LocalDateTime.now());
        fileRecord.setUpdateTime(LocalDateTime.now());

        // 5. 初始化处理状态为处理中（库存文件需要异步解析）
        String initialStatus = buildInitialStatusJson();
        fileRecord.setStatus(ValueAddedProcessStatus.PROCESSING.getCode());
        fileRecord.setRemark(initialStatus);

        if (!save(fileRecord)) {
            throw new RuntimeException("保存文件记录失败");
        }

        log.info("Stock excel file saved successfully, fileId: {}, deliveryOrderNo: {}", fileRecord.getId(), deliveryOrderNo);

        // 6. 异步解析Excel文件
        try {
            // 注入库存服务进行异步解析
            IValueAddedStockService stockService = SpringUtils.getBean(IValueAddedStockService.class);
            stockService.parseAndSaveStockExcel(fileRecord.getId(), deliveryOrderNo.trim());

            log.info("Stock excel file async parsing started, fileId: {}", fileRecord.getId());
        } catch (Exception e) {
            log.error("Failed to start async parsing for stock excel file, fileId: {}", fileRecord.getId(), e);
            // 更新文件状态为处理失败
            updateFileProcessStatus(fileRecord.getId(), ValueAddedProcessStatus.FAILED.getCode(),
                "启动异步解析失败：" + e.getMessage());
        }

        return fileRecord.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFile(Long fileId, String deliveryOrderNo) {
        log.info("Deleting file, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);

        // 1. 参数验证
        if (fileId == null) {
            throw new IllegalArgumentException("文件ID不能为空");
        }
        if (deliveryOrderNo == null || deliveryOrderNo.trim().isEmpty()) {
            throw new IllegalArgumentException("交付单编号不能为空");
        }

        // 2. 查询文件记录
        ValueAddedFile fileRecord = getById(fileId);
        if (fileRecord == null) {
            throw new IllegalArgumentException("文件不存在");
        }

        // 3. 权限验证：验证文件属于指定交付单
        if (!deliveryOrderNo.trim().equals(fileRecord.getDeliveryOrderNo())) {
            throw new IllegalArgumentException("无权限删除该文件");
        }

        // 4. 状态检查：验证文件未被删除
        if (Boolean.TRUE.equals(fileRecord.getIsDel())) {
            throw new IllegalArgumentException("文件已被删除");
        }

        // 5. 执行软删除
        fileRecord.setIsDel(true);
        fileRecord.setUpdateTime(LocalDateTime.now());

        boolean result = updateById(fileRecord);
        if (result) {
            log.info("File deleted successfully, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);
        } else {
            log.error("Failed to delete file, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);
        }

        return result;
    }

    @Override
    public String getFileDownloadUrl(Long fileId, String deliveryOrderNo) {
        log.info("Getting file download URL, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);

        // 1. 参数验证
        if (fileId == null) {
            throw new IllegalArgumentException("文件ID不能为空");
        }
        if (deliveryOrderNo == null || deliveryOrderNo.trim().isEmpty()) {
            throw new IllegalArgumentException("交付单编号不能为空");
        }

        // 2. 查询文件记录
        ValueAddedFile fileRecord = getById(fileId);
        if (fileRecord == null) {
            throw new IllegalArgumentException("文件不存在");
        }

        // 3. 权限验证：验证文件属于指定交付单
        if (!deliveryOrderNo.trim().equals(fileRecord.getDeliveryOrderNo())) {
            throw new IllegalArgumentException("无权限访问该文件");
        }

        // 4. 状态检查：验证文件未被删除
        if (Boolean.TRUE.equals(fileRecord.getIsDel())) {
            throw new IllegalArgumentException("文件已被删除");
        }

        // 5. 获取完整下载URL
        try {
            String fullFileUrl = remoteFileService.getFullFileUrl(fileRecord.getFileUrl()).getData();
            if (fullFileUrl == null || fullFileUrl.trim().isEmpty()) {
                throw new RuntimeException("获取文件下载地址失败");
            }

            log.info("File download URL retrieved successfully, fileId: {}, deliveryOrderNo: {}",
                    fileId, deliveryOrderNo);
            return fullFileUrl;
        } catch (Exception e) {
            log.error("Failed to get file download URL, fileId: {}, deliveryOrderNo: {}",
                    fileId, deliveryOrderNo, e);
            throw new RuntimeException("获取文件下载地址失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFileProcessStatus(Long fileId, Integer status, String statusInfo) {
        log.info("Updating file process status, fileId: {}, status: {}", fileId, status);

        ValueAddedFile fileRecord = getById(fileId);
        if (fileRecord == null) {
            log.warn("File record not found, fileId: {}", fileId);
            return;
        }

        // 更新状态字段和备注信息
        fileRecord.setStatus(status);
        fileRecord.setRemark(statusInfo);
        fileRecord.setUpdateTime(LocalDateTime.now());

        if (!updateById(fileRecord)) {
            log.error("Failed to update file process status, fileId: {}", fileId);
            throw new RuntimeException("更新文件处理状态失败");
        }

        log.info("File process status updated successfully, fileId: {}, status: {}", fileId, status);
    }

    @Override
    public String getFileProcessStatus(Long fileId) {
        ValueAddedFile fileRecord = getById(fileId);
        if (fileRecord == null) {
            return buildErrorStatusJson("文件记录不存在");
        }
        return fileRecord.getRemark();
    }

    @Override
    public ValueAddedFile getFileById(Long fileId) {
        return getById(fileId);
    }

    /**
     * 构建初始状态JSON
     * 使用FileStatusInfo DTO统一处理
     */
    private String buildInitialStatusJson() {
        try {
            FileStatusInfo statusInfo = FileStatusInfo.builder()
                    .status(ValueAddedProcessStatus.PROCESSING.getCode())
                    .message("文件已上传，等待处理")
                    .totalCount(0)
                    .successCount(0)
                    .failCount(0)
                    .hasErrorFile(false)
                    .updateTime(System.currentTimeMillis())
                    .build();

            // 使用FastJSON2替代ObjectMapper
            return JSON.toJSONString(statusInfo);
        } catch (Exception e) {
            log.error("Build initial status JSON failed", e);
            return "{\"status\":0,\"message\":\"文件已上传，等待处理\"}";
        }
    }

    /**
     * 构建错误状态JSON
     * 用于文件记录不存在等错误情况
     */
    private String buildErrorStatusJson(String message) {
        try {
            FileStatusInfo statusInfo = FileStatusInfo.builder()
                    .status(ValueAddedProcessStatus.FAILED.getCode())
                    .message(message)
                    .totalCount(0)
                    .successCount(0)
                    .failCount(0)
                    .hasErrorFile(false)
                    .updateTime(System.currentTimeMillis())
                    .build();

            return JSON.toJSONString(statusInfo);
        } catch (Exception e) {
            log.error("Build error status JSON failed", e);
            return "{\"status\":2,\"message\":\"" + message + "\"}";
        }
    }
}
