package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 增值库存对象 c_value_added_stock
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
@Data
@ApiModel("增值库存对象")
@Accessors(chain = true)
@TableName("c_value_added_stock")
public class ValueAddedStock extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 增值交付单编号 */
    @Excel(name = "增值交付单编号")
    @TableField("delivery_order_no")
    @ApiModelProperty(value = "增值交付单编号")
    private String deliveryOrderNo;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private String period;

    /** 解析结果，1-正常，2-异常 */
    @Excel(name = "解析结果", readConverterExp = "1=正常,2=异常")
    @TableField("analysis_result")
    @ApiModelProperty(value = "解析结果，1-正常，2-异常")
    private Integer analysisResult;

    /** 库存数 */
    @Excel(name = "库存数")
    @TableField("stock_count")
    @ApiModelProperty(value = "库存数")
    private Integer stockCount;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;
}
