package com.bxm.customer.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 日期格式解析工具类
 *
 * 支持多种日期格式的解析，将其统一转换为yyyyMM格式：
 * 1. yyyy-MM (如: 2024-01)
 * 2. yyyyMM (如: 202401)
 * 3. yyyy年MM月 (如: 2024年01月)
 * 4. yyyy年M月 (如: 2024年1月)
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Slf4j
@Component
public class DateFormatParser {

    // 日期格式正则表达式
    private static final Pattern PATTERN_YYYY_MM = Pattern.compile("^(\\d{4})-(\\d{1,2})$");
    private static final Pattern PATTERN_YYYYMM = Pattern.compile("^(\\d{6})$");
    private static final Pattern PATTERN_CHINESE_FULL = Pattern.compile("^(\\d{4})年(\\d{1,2})月$");
    
    // 输出格式
    private static final DateTimeFormatter OUTPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");

    /**
     * 解析账期字符串，返回标准格式的账期（yyyyMM）
     *
     * @param periodStr 账期字符串
     * @return 标准格式的账期，解析失败返回null
     */
    public String parsePeriod(String periodStr) {
        if (periodStr == null || periodStr.trim().isEmpty()) {
            return null;
        }

        String trimmed = periodStr.trim();
        log.debug("Parsing period string: {}", trimmed);

        try {
            // 1. 尝试解析 yyyy-MM 格式
            Matcher matcher1 = PATTERN_YYYY_MM.matcher(trimmed);
            if (matcher1.matches()) {
                String year = matcher1.group(1);
                String month = matcher1.group(2);
                return formatPeriod(year, month);
            }

            // 2. 尝试解析 yyyyMM 格式
            Matcher matcher2 = PATTERN_YYYYMM.matcher(trimmed);
            if (matcher2.matches()) {
                String yyyyMM = matcher2.group(1);
                // 验证是否为有效日期
                if (isValidYearMonth(yyyyMM)) {
                    return yyyyMM;
                }
            }

            // 3. 尝试解析中文格式 yyyy年MM月 或 yyyy年M月
            Matcher matcher3 = PATTERN_CHINESE_FULL.matcher(trimmed);
            if (matcher3.matches()) {
                String year = matcher3.group(1);
                String month = matcher3.group(2);
                return formatPeriod(year, month);
            }

            log.warn("Unable to parse period string: {}", trimmed);
            return null;

        } catch (Exception e) {
            log.error("Error parsing period string: {}", trimmed, e);
            return null;
        }
    }

    /**
     * 格式化年月为yyyyMM格式
     *
     * @param year 年份字符串
     * @param month 月份字符串
     * @return 格式化后的账期
     */
    private String formatPeriod(String year, String month) {
        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month);

            // 验证年份和月份的有效性
            if (yearInt < 1900 || yearInt > 2100) {
                log.warn("Invalid year: {}", yearInt);
                return null;
            }
            if (monthInt < 1 || monthInt > 12) {
                log.warn("Invalid month: {}", monthInt);
                return null;
            }

            // 构造LocalDate进行验证
            LocalDate date = LocalDate.of(yearInt, monthInt, 1);
            return date.format(OUTPUT_FORMATTER);

        } catch (Exception e) {
            log.error("Error formatting period, year: {}, month: {}", year, month, e);
            return null;
        }
    }

    /**
     * 验证yyyyMM格式的字符串是否为有效日期
     *
     * @param yyyyMM 6位数字字符串
     * @return 是否有效
     */
    private boolean isValidYearMonth(String yyyyMM) {
        try {
            if (yyyyMM.length() != 6) {
                return false;
            }

            String year = yyyyMM.substring(0, 4);
            String month = yyyyMM.substring(4, 6);

            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month);

            // 验证年份和月份的有效性
            if (yearInt < 1900 || yearInt > 2100) {
                return false;
            }
            if (monthInt < 1 || monthInt > 12) {
                return false;
            }

            // 构造LocalDate进行最终验证
            LocalDate.of(yearInt, monthInt, 1);
            return true;

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查账期字符串是否为支持的格式
     *
     * @param periodStr 账期字符串
     * @return 是否为支持的格式
     */
    public boolean isSupportedFormat(String periodStr) {
        if (periodStr == null || periodStr.trim().isEmpty()) {
            return false;
        }

        String trimmed = periodStr.trim();
        
        return PATTERN_YYYY_MM.matcher(trimmed).matches() ||
               PATTERN_YYYYMM.matcher(trimmed).matches() ||
               PATTERN_CHINESE_FULL.matcher(trimmed).matches();
    }

    /**
     * 获取支持的日期格式说明
     *
     * @return 格式说明字符串
     */
    public String getSupportedFormats() {
        return "支持的日期格式：yyyy-MM (如: 2024-01)、yyyyMM (如: 202401)、yyyy年MM月 (如: 2024年01月)、yyyy年M月 (如: 2024年1月)";
    }
}
