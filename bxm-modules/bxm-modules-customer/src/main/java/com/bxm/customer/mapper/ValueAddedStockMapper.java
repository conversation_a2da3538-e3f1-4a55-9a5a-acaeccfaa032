package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.ValueAddedStock;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 增值库存Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Mapper
public interface ValueAddedStockMapper extends BaseMapper<ValueAddedStock> {

    /**
     * 根据交付单编号查询库存记录
     *
     * @param deliveryOrderNo 交付单编号
     * @return 库存记录列表
     */
    List<ValueAddedStock> selectByDeliveryOrderNo(@Param("deliveryOrderNo") String deliveryOrderNo);

    /**
     * 根据交付单编号和账期查询库存记录
     *
     * @param deliveryOrderNo 交付单编号
     * @param period 账期
     * @return 库存记录
     */
    ValueAddedStock selectByDeliveryOrderNoAndPeriod(@Param("deliveryOrderNo") String deliveryOrderNo, 
                                                     @Param("period") String period);

    /**
     * 批量插入库存记录
     *
     * @param stockList 库存记录列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("list") List<ValueAddedStock> stockList);

    /**
     * 根据交付单编号删除库存记录
     *
     * @param deliveryOrderNo 交付单编号
     * @return 删除的记录数
     */
    int deleteByDeliveryOrderNo(@Param("deliveryOrderNo") String deliveryOrderNo);
}
