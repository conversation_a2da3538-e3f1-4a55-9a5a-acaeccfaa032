package com.bxm.customer.utils;

import com.bxm.customer.domain.ValueAddedStock;
import com.bxm.common.core.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 库存Excel解析器
 *
 * 解析Excel文件中的库存数据：
 * 1. 遍历所有Sheet
 * 2. 解析Sheet名称为账期（支持多种日期格式）
 * 3. 读取第一列数据，找到最大值作为库存数
 * 4. 根据解析结果判断成功或失败
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Slf4j
@Component
public class StockExcelParser {

    @Autowired
    private DateFormatParser dateFormatParser;

    /**
     * 解析库存Excel文件
     *
     * @param fileUrl 文件URL
     * @param deliveryOrderNo 交付单编号
     * @return 库存记录列表
     * @throws Exception 解析失败时抛出异常
     */
    public List<ValueAddedStock> parseStockExcel(String fileUrl, String deliveryOrderNo) throws Exception {
        log.info("Starting parse stock excel, fileUrl: {}, deliveryOrderNo: {}", fileUrl, deliveryOrderNo);

        List<ValueAddedStock> stockList = new ArrayList<>();

        try (InputStream inputStream = new URL(fileUrl).openStream()) {
            Workbook workbook = WorkbookFactory.create(inputStream);
            
            // 遍历所有Sheet
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                String sheetName = sheet.getSheetName();
                
                log.info("Processing sheet: {}", sheetName);
                
                // 解析Sheet名称为账期
                String period = dateFormatParser.parsePeriod(sheetName);
                if (period == null) {
                    log.warn("Failed to parse period from sheet name: {}", sheetName);
                    // 创建解析失败的记录
                    ValueAddedStock failedStock = createStockRecord(deliveryOrderNo, sheetName, 2, 0, 
                        "Sheet名称无法解析为有效的账期格式");
                    stockList.add(failedStock);
                    continue;
                }
                
                // 读取第一列数据，找到最大值
                Integer maxValue = findMaxValueInFirstColumn(sheet);
                if (maxValue == null) {
                    log.warn("No valid numeric data found in first column of sheet: {}", sheetName);
                    // 创建解析失败的记录
                    ValueAddedStock failedStock = createStockRecord(deliveryOrderNo, period, 2, 0, 
                        "第一列中未找到有效的数值数据");
                    stockList.add(failedStock);
                    continue;
                }
                
                // 创建成功的库存记录
                ValueAddedStock successStock = createStockRecord(deliveryOrderNo, period, 1, maxValue, 
                    "解析成功");
                stockList.add(successStock);
                
                log.info("Successfully parsed sheet: {}, period: {}, maxValue: {}", sheetName, period, maxValue);
            }
            
            workbook.close();
            
        } catch (Exception e) {
            log.error("Failed to parse stock excel file", e);
            throw new Exception("解析Excel文件失败：" + e.getMessage(), e);
        }

        log.info("Stock excel parsing completed, total records: {}", stockList.size());
        return stockList;
    }

    /**
     * 解析库存Excel文件（从MultipartFile）
     *
     * @param excelFile Excel文件
     * @param deliveryOrderNo 交付单编号
     * @return 库存记录列表
     * @throws Exception 解析失败时抛出异常
     */
    public List<ValueAddedStock> parseStockExcelFromMultipartFile(MultipartFile excelFile, String deliveryOrderNo) throws Exception {
        log.info("Starting parse stock excel from multipart file, fileName: {}, deliveryOrderNo: {}",
            excelFile.getOriginalFilename(), deliveryOrderNo);

        List<ValueAddedStock> stockList = new ArrayList<>();

        try (InputStream inputStream = excelFile.getInputStream()) {
            Workbook workbook = WorkbookFactory.create(inputStream);

            // 遍历所有Sheet
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                String sheetName = sheet.getSheetName();

                log.info("Processing sheet: {}", sheetName);

                // 解析Sheet名称为账期
                String period = dateFormatParser.parsePeriod(sheetName);
                if (period == null) {
                    log.warn("Failed to parse period from sheet name: {}", sheetName);
                    // 创建解析失败的记录
                    ValueAddedStock failedStock = createStockRecord(deliveryOrderNo, sheetName, 2, 0,
                        "Sheet名称无法解析为有效的账期格式");
                    stockList.add(failedStock);
                    continue;
                }

                // 读取第一列数据，找到最大值
                Integer maxValue = findMaxValueInFirstColumn(sheet);
                if (maxValue == null) {
                    log.warn("No valid numeric data found in first column of sheet: {}", sheetName);
                    // 创建解析失败的记录
                    ValueAddedStock failedStock = createStockRecord(deliveryOrderNo, period, 2, 0,
                        "第一列中未找到有效的数值数据");
                    stockList.add(failedStock);
                    continue;
                }

                // 创建成功的库存记录
                ValueAddedStock successStock = createStockRecord(deliveryOrderNo, period, 1, maxValue,
                    "解析成功");
                stockList.add(successStock);

                log.info("Successfully parsed sheet: {}, period: {}, maxValue: {}", sheetName, period, maxValue);
            }

            workbook.close();

        } catch (Exception e) {
            log.error("Failed to parse stock excel from multipart file", e);
            throw new Exception("解析Excel文件失败：" + e.getMessage(), e);
        }

        log.info("Stock excel parsing completed, total records: {}", stockList.size());
        return stockList;
    }

    /**
     * 在Sheet的第一列中查找最大数值
     *
     * @param sheet Excel Sheet
     * @return 最大值，如果没有找到有效数值则返回null
     */
    private Integer findMaxValueInFirstColumn(Sheet sheet) {
        Integer maxValue = null;
        
        // 遍历所有行
        for (Row row : sheet) {
            if (row == null) continue;
            
            // 获取第一列的单元格
            Cell firstCell = row.getCell(0);
            if (firstCell == null) continue;
            
            // 尝试获取数值
            Integer cellValue = getCellNumericValue(firstCell);
            if (cellValue != null) {
                if (maxValue == null || cellValue > maxValue) {
                    maxValue = cellValue;
                }
            }
        }
        
        return maxValue;
    }

    /**
     * 获取单元格的数值（支持数字和文本格式的数字）
     *
     * @param cell 单元格
     * @return 数值，如果无法解析则返回null
     */
    private Integer getCellNumericValue(Cell cell) {
        try {
            switch (cell.getCellType()) {
                case NUMERIC:
                    return (int) cell.getNumericCellValue();
                case STRING:
                    String stringValue = cell.getStringCellValue().trim();
                    if (stringValue.isEmpty()) return null;
                    // 尝试解析字符串为数字
                    return Integer.parseInt(stringValue);
                case FORMULA:
                    // 对于公式单元格，尝试获取计算后的值
                    switch (cell.getCachedFormulaResultType()) {
                        case NUMERIC:
                            return (int) cell.getNumericCellValue();
                        case STRING:
                            String formulaStringValue = cell.getStringCellValue().trim();
                            if (formulaStringValue.isEmpty()) return null;
                            return Integer.parseInt(formulaStringValue);
                        default:
                            return null;
                    }
                default:
                    return null;
            }
        } catch (Exception e) {
            // 解析失败，返回null
            return null;
        }
    }

    /**
     * 创建库存记录
     *
     * @param deliveryOrderNo 交付单编号
     * @param period 账期
     * @param analysisResult 解析结果（1-正常，2-异常）
     * @param stockCount 库存数
     * @param remark 备注
     * @return 库存记录
     */
    private ValueAddedStock createStockRecord(String deliveryOrderNo, String period, Integer analysisResult,
                                            Integer stockCount, String remark) {
        ValueAddedStock stock = new ValueAddedStock();
        stock.setDeliveryOrderNo(deliveryOrderNo);
        stock.setPeriod(period);
        stock.setAnalysisResult(analysisResult);
        stock.setStockCount(stockCount);
        stock.setRemark(remark);
        stock.setCreateTime(LocalDateTime.now());
        stock.setUpdateTime(LocalDateTime.now());

        // 从当前登录用户获取创建人和更新人信息
        String currentUser = SecurityUtils.getUsername();
        stock.setCreateBy(currentUser);
        stock.setUpdateBy(currentUser);

        return stock;
    }
}
