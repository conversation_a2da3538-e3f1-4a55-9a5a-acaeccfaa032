package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.ValueAddedStock;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.enums.ValueAddedProcessStatus;
import com.bxm.customer.mapper.ValueAddedStockMapper;
import com.bxm.customer.service.IValueAddedStockService;
import com.bxm.customer.service.IValueAddedFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 增值库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Slf4j
@Service
public class ValueAddedStockServiceImpl extends ServiceImpl<ValueAddedStockMapper, ValueAddedStock>
        implements IValueAddedStockService {

    @Autowired
    private IValueAddedFileService valueAddedFileService;

    @Override
    public void parseAndSaveStockExcel(Long fileId, String deliveryOrderNo) {
        // 此方法已不再使用，改为同步处理
        log.warn("parseAndSaveStockExcel method is deprecated, use synchronous processing instead");
    }

    @Override
    public List<ValueAddedStock> getByDeliveryOrderNo(String deliveryOrderNo) {
        LambdaQueryWrapper<ValueAddedStock> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedStock::getDeliveryOrderNo, deliveryOrderNo);
        return list(queryWrapper);
    }

    @Override
    public ValueAddedStock getByDeliveryOrderNoAndPeriod(String deliveryOrderNo, String period) {
        LambdaQueryWrapper<ValueAddedStock> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedStock::getDeliveryOrderNo, deliveryOrderNo)
                   .eq(ValueAddedStock::getPeriod, period);
        return getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveOrUpdate(List<ValueAddedStock> stockList) {
        if (stockList == null || stockList.isEmpty()) {
            return true;
        }

        try {
            // 使用MyBatis Plus的批量保存或更新
            return saveOrUpdateBatch(stockList);
        } catch (Exception e) {
            log.error("Batch save or update stock failed", e);
            throw new RuntimeException("批量保存库存数据失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByDeliveryOrderNo(String deliveryOrderNo) {
        LambdaQueryWrapper<ValueAddedStock> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedStock::getDeliveryOrderNo, deliveryOrderNo);
        return remove(queryWrapper);
    }


}
